#include "binding.h"
#include "UnityResolve.hpp"
#include "luaaa.hpp"

void reg_UnityResolve(lua_State *L) {
    luaaa::LuaClass<UnityResolve> UnityResolve_class(L, "UnityResolve");
    UnityResolve_class.ctor();
    UnityResolve_class.fun("Get", &UnityResolve::Get);
    luaaa::LuaClass<UnityResolve::Assembly> Assembly_class(L, "Assembly");
    Assembly_class.ctor();
    Assembly_class.fun("Get", &UnityResolve::Assembly::Get);
    luaaa::LuaClass<UnityResolve::Class> Class_class(L, "Class");
    Class_class.fun("Get", &UnityResolve::Class::Get<UnityResolve::Method>);
    Class_class.ctor();
    luaaa::LuaClass<UnityResolve::Method> Method_class(L, "Method");
    Method_class.ctor();
    Method_class.fun("Invoke", &UnityResolve::Method::Invoke<void*>);
    Method_class.fun("GetAddress", &UnityResolve::Method::GetAddress);
}
