#pragma once

#include <string>
#include <vector>
#include <iostream>
#include <dirent.h>
#include <sys/types.h>
#include <filesystem>
#include "dobby.h"
#include "format.h"

#define SYM(lib, name, ret, ...) \
  reinterpret_cast<ret( *)(__VA_ARGS__)>(xdl_sym(lib, name, NULL))

#define FUN(ptr, ret, ...) \
  reinterpret_cast<ret( *)(__VA_ARGS__)>(ptr)

#define FUNC(ret, func, ...) \
  ret (*orig_##func)(__VA_ARGS__); \
  ret hook_##func(__VA_ARGS__)

#define HOOK(target, func) DobbyHook((void *)target,(void *)hook_##func,(void **)&orig_##func)

#define HOOKMETHOD(clazz,method,func) \
    DobbyHook((void *)clazz->Get<UnityResolve::Method>(method)->function,(void *)hook_##func,(void **)&orig_##func)

#define OFFSET(structure, member) (int)&(structure)->member

#define TIME (long)(std::chrono::system_clock::now().time_since_epoch().count()/1000)


void *get_module(std::string name) {
    auto lib = fmt::format("lib{0}.so", name).c_str();
    auto handle = xdl_open(lib, 0);
    while (!handle) {
        handle = xdl_open(lib, 0);
        sleep(1);
    }
    return handle;
}