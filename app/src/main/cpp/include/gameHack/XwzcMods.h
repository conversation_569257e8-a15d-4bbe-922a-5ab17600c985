//
// Created by 33735 on 2025/06/24.
//

#pragma once

#include "imgui.h"
#include "xwzcMods/SharedDataManager.h"
#include "xwzcMods/ObserverHack.h"
#include "xwzcMods/PlayerListHack.h"
#include "xwzcMods/DrawHack.h"
#include "xwzcMods/PlayerSelfHack.h"
#include "xdl.h"
#include "Il2Cpp.h"
#include "UnityResolve.hpp"
#include <string>
#include <thread>
#include <chrono>
#include "format.h"

namespace XwzcMods {

    // 全局初始化状态
    bool g_isInitializing = false;
    bool g_mainInitialized = false;
    std::string g_initStatusMessage = "等待初始化...";
    int g_initAttempts = 0;
    const int g_maxInitAttempts = 3;

    // 初始化所有功能模块
    void InitAllMods() {
            SharedDataManager::Init(); // 首先初始化共享数据管理器
            ObserverHack::Init();
            PlayerListHack::Init();
            DrawHack::Init();
            PlayerSelfHack::Init();
    }

    // 后台循环线程，驱动所有功能
    void BackgroundLoop() {
        while (true) {
            SharedDataManager::Loop(); // 首先更新共享数据
            ObserverHack::Loop();
            PlayerListHack::Loop();
            DrawHack::Loop();
            PlayerSelfHack::Loop();
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }

    // 检查Il2Cpp是否准备就绪
    bool IsIl2CppReady(void* handle) {
        // 检查关键函数是否都能获取到
        auto domain_get = xdl_sym(handle, "il2cpp_domain_get", nullptr);
        auto domain_get_assemblies = xdl_sym(handle, "il2cpp_domain_get_assemblies", nullptr);
        auto assembly_get_image = xdl_sym(handle, "il2cpp_assembly_get_image", nullptr);

        if (!domain_get || !domain_get_assemblies || !assembly_get_image) {
            return false;
        }

        // 尝试获取域并检查是否有程序集
        try {
            auto domain_func = (void*(*)())domain_get;
            void* domain = domain_func();
            if (!domain) return false;

            auto assemblies_func = (void**(*)(const void*, size_t*))domain_get_assemblies;
            size_t count = 0;
            void** assemblies = assemblies_func(domain, &count);

            // 至少要有一些程序集才算准备好
            return (assemblies != nullptr && count > 0);
        } catch (...) {
            return false;
        }
    }

    // 稳定的域获取函数
    void* GetIl2CppDomainStable(void* handle) {
        auto domain_get_func = (void*(*)())xdl_sym(handle, "il2cpp_domain_get", nullptr);
        if (!domain_get_func) return nullptr;

        for (int attempt = 0; attempt < 50; attempt++) {
            try {
                void* domain = domain_get_func();
                if (domain) {
                    // 验证域是否真的可用
                    auto assemblies_func = (void**(*)(const void*, size_t*))xdl_sym(handle, "il2cpp_domain_get_assemblies", nullptr);
                    if (assemblies_func) {
                        size_t count = 0;
                        void** assemblies = assemblies_func(domain, &count);
                        if (assemblies && count > 0) {
                            return domain; // 域可用且有程序集
                        }
                    }
                }
            } catch (...) {
                // 忽略异常继续重试
            }

            // 动态调整等待时间
            int wait_time = 100 + (attempt * 50); // 100ms到2.5s递增
            if (wait_time > 2500) wait_time = 2500;
            std::this_thread::sleep_for(std::chrono::milliseconds(wait_time));
        }
        return nullptr;
    }

    // 主初始化函数
    void DoMainInit() {
        g_isInitializing = true;
        g_initStatusMessage = fmt::format("开始第 {} 次初始化尝试...", g_initAttempts);

        g_initStatusMessage = "正在打开 Il2Cpp 库...";
        auto il2cpp_handle = xdl_open("libil2cpp.so", 0);
        if (!il2cpp_handle) {
            g_initStatusMessage = "错误: 无法打开 libil2cpp.so - 请确保游戏已完全加载";
            g_isInitializing = false;
            return;
        }

        g_initStatusMessage = "正在附加到 Il2Cpp...";
        Il2CppAttach(il2cpp_handle);

        // 等待Il2Cpp完全准备就绪
        g_initStatusMessage = "等待 Il2Cpp 完全准备就绪...";
        bool ready = false;
        for (int i = 0; i < 30; i++) {
            if (IsIl2CppReady(il2cpp_handle)) {
                ready = true;
                break;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            g_initStatusMessage = fmt::format("等待 Il2Cpp 准备就绪... ({}/30)", i + 1);
        }

        if (!ready) {
            g_initStatusMessage = "错误: Il2Cpp 未能在预期时间内准备就绪";
            g_isInitializing = false;
            return;
        }

        // 稳定获取域
        g_initStatusMessage = "正在获取 Il2Cpp 域...";
        void* domain = GetIl2CppDomainStable(il2cpp_handle);

        if (!domain) {
            g_initStatusMessage = "错误: Il2Cpp 域获取失败 - 请稍后重试或重启游戏";
            g_isInitializing = false;
            return;
        }

        g_initStatusMessage = "域获取成功，跳过 UnityResolve 初始化...";
        // 完全跳过UnityResolve::Init，因为它会遍历所有程序集导致崩溃
        // 我们的代码主要使用Il2CppGetMethodOffset等直接函数，不依赖UnityResolve

        g_initStatusMessage = "跳过 UnityResolve，使用直接 Il2Cpp 调用";

        g_initStatusMessage = "正在初始化功能模块...";
        try {
            InitAllMods();
        } catch (...) {
            g_initStatusMessage = "错误: 功能模块初始化失败";
            g_isInitializing = false;
            return;
        }

        g_initStatusMessage = "正在启动后台服务...";
        try {
            std::thread(BackgroundLoop).detach();
        } catch (...) {
            g_initStatusMessage = "错误: 后台服务启动失败";
            g_isInitializing = false;
            return;
        }

        g_initStatusMessage = "初始化完成，一切就绪!";
        g_mainInitialized = true;
        g_isInitializing = false;
    }

    // 绘制主菜单
    void Draw() {
        ImGui::SetNextWindowSize(ImVec2(800, 600), ImGuiCond_FirstUseEver);
        ImGui::SetNextWindowPos(ImVec2(50, 50), ImGuiCond_FirstUseEver);

        if (ImGui::Begin("希望之村辅助")) {

        if (!g_mainInitialized) {
            if (g_isInitializing) {
                ImGui::Text("正在初始化，请稍候...");
                ImGui::ProgressBar(-1.0f * ImGui::GetTime(), ImVec2(-1, 0), "");
            } else {
                // 显示重试信息
                if (g_initAttempts > 0) {
                    ImGui::TextColored(ImVec4(1,0.5f,0,1), "尝试次数: %d/%d", g_initAttempts, g_maxInitAttempts);
                    if (g_initAttempts >= g_maxInitAttempts) {
                        ImGui::TextColored(ImVec4(1,0,0,1), "多次初始化失败，请重启游戏后再试");
                    }
                }

                // 初始化按钮
                bool canRetry = g_initAttempts < g_maxInitAttempts;
                if (!canRetry) {
                    ImGui::PushStyleVar(ImGuiStyleVar_Alpha, 0.5f);
                }

                if (ImGui::Button("!!! 初始化辅助 !!!", ImVec2(-1, 40)) && canRetry) {
                    g_initAttempts++;
                    std::thread(DoMainInit).detach();
                }

                if (!canRetry) {
                    ImGui::PopStyleVar();
                    ImGui::SameLine();
                    if (ImGui::Button("重置重试次数")) {
                        g_initAttempts = 0;
                        g_initStatusMessage = "等待初始化...";
                    }
                }
            }
            ImGui::Separator();
            ImGui::Text("状态: %s", g_initStatusMessage.c_str());
            ImGui::TextWrapped("请在进入游戏大厅或稳定场景后，再点击此按钮。");

            // 添加一些调试信息和建议
            if (g_initAttempts > 0) {
                ImGui::Separator();
                ImGui::TextColored(ImVec4(0.7f,0.7f,0.7f,1), "提示:");
                ImGui::BulletText("确保游戏已完全加载到主界面");
                ImGui::BulletText("如果反复失败，尝试重启游戏");
                ImGui::BulletText("某些游戏版本可能需要更长初始化时间");
            }
        } else {
            ImGui::TextColored(ImVec4(0,1,0,1), "辅助已就绪!");
            ImGui::Separator();

            if (ImGui::CollapsingHeader("观察者模式")) {
                ObserverHack::DrawUI();
            }

            if (ImGui::CollapsingHeader("玩家功能")) {
                PlayerListHack::DrawUI();
            }

            if (ImGui::CollapsingHeader("玩家自身功能")) {
                PlayerSelfHack::DrawUI();
            }

            if (ImGui::CollapsingHeader("绘制功能")) {
                DrawHack::DrawUI();
            }
        }
        } // End of main window
        ImGui::End();

        // 参考 DrawHack.exmple：在ImGui帧内、窗口结束前调用绘制（GL线程）
        try { DrawHack::Render(); } catch (...) {}


    }

} // namespace XwzcMods