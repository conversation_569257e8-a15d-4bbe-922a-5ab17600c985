//
// Created by 33735 on 2025/06/24.
//

#pragma once

#include "Il2Cpp.h"
#include "UnityResolve.hpp"
#include <vector>
#include <string>

namespace GameHelper {
    // 动态获取函数地址 - 使用直接Il2Cpp调用替代UnityResolve
    void* GetMethodAddress(const char* assemblyName, const char* namespaze, const char* klassName, const char* methodName, int argCount = 0) {
        // 直接使用Il2CppGetMethodOffset，避免依赖UnityResolve
        return Il2CppGetMethodOffset(assemblyName, namespaze, klassName, methodName, argCount);
    }

    // 获取字段偏移 - 使用直接Il2Cpp调用替代UnityResolve
    int32_t GetFieldOffset(const char* assemblyName, const char* namespaze, const char* klassName, const char* fieldName) {
        // 直接使用Il2CppGetFieldOffset，避免依赖UnityResolve
        size_t offset = Il2CppGetFieldOffset(assemblyName, namespaze, klassName, fieldName);
        return (offset == 0) ? -1 : (int32_t)offset;
    }

    // 获取GameServerManager实例 - 增强版本
    void* getGameServerManager() {
        void* instance = nullptr;
        try {
            Il2CppGetStaticFieldValue("Assembly-CSharp.dll", "", "GameServerManager", "s_instance", &instance);
        } catch (...) {
            // 静态字段获取失败
            instance = nullptr;
        }
        return instance;
    }
} 