#pragma once

#include "imgui.h"
#include "GameHelper.h"
#include "SharedDataManager.h"  // 使用SharedDataManager替代PlayerListHack
#include <vector>
#include <string>
#include <map>
#include <algorithm>
#include <unordered_set>
#include "Unity/Vector2.hpp"
#include "Unity/Vector3.hpp"

namespace DrawHack {

    //================ 函数指针定义 ================//
    // 注意：现在使用SharedDataManager中的共享函数指针和类型缓存

    //================ 智能PlayerController实例管理 ================//
    // 注意：实例管理现在由SharedDataManager统一处理

    // 前向声明清理函数
    namespace PlayerRays { void ClearRenderData(); }
    namespace ItemESP { void ClearRenderData(); }

    //================ 玩家射线功能模块 ================//
    namespace PlayerRays {
        // 数据
        std::vector<ImVec2> g_player_screen_positions;

        // 功能开关与参数
        bool g_enable_esp = false;
        float g_line_thickness = 1.5f;
        float g_y_offset = 50.0f;
        bool g_exclude_self = true;

        // 新增：射线颜色和起始点设置
        ImVec4 g_ray_color = ImVec4(1.0f, 0.0f, 0.0f, 1.0f); // 红色
        int g_ray_origin = 0; // 0=顶部中心, 1=上方中间, 2=底部中心, 3=屏幕中心

        // 调试信息：PlayerController实例数量
        int g_player_controller_count = 0;
        int g_last_collected_players = 0;

        // 函数声明
        void Loop();
        void Render();
        void DrawUI();
    }

    //================ 物品绘制功能模块 ================//
    namespace ItemESP {
        // 线程安全与数据
        struct ItemRenderInfo {
            ImVec2 screen_pos;
            int id;
            ImVec4 color; // 新增：每个物品的颜色
        };
        std::vector<ItemRenderInfo> g_item_render_info;
        std::map<int, int> g_item_counts;

        // 功能开关与参数
        bool g_enable_esp = false;
        bool g_enable_lines = false;
        bool g_show_counter = false;
        bool g_show_only_home_markers = false;
        float g_font_size = 14.0f;
        float g_line_thickness = 1.0f;

        // ID 过滤器数据
        char g_id_input_buffer[20] = "";
        std::vector<int> g_id_filter_list;
        bool g_is_blacklist_mode = false;

        // 新增：自定义颜色系统
        std::map<int, ImVec4> g_custom_colors; // ID -> 颜色映射
        ImVec4 g_default_color = ImVec4(1.0f, 1.0f, 0.0f, 1.0f); // 默认黄色
        int g_color_setting_id = 0; // 当前设置颜色的物品ID
        ImVec4 g_temp_color = ImVec4(1.0f, 1.0f, 0.0f, 1.0f); // 临时颜色

        // 函数声明
        void Loop();
        void Render();
        void RenderCounter();
        void DrawUI();
    }

    //================ 虚拟数字键盘系统 ================//
    bool g_show_virtual_keyboard = false;
    char* g_keyboard_target_buffer = nullptr;
    size_t g_keyboard_target_buffer_size = 0;
    const char* g_keyboard_title = "键盘";

    void ShowVirtualKeyboard() {
        if (!g_show_virtual_keyboard || !g_keyboard_target_buffer) return;

        // 增大窗口尺寸，确保键盘显示完整
        ImGui::SetNextWindowSize(ImVec2(400, 500), ImGuiCond_FirstUseEver);
        ImGui::SetNextWindowPos(ImVec2(ImGui::GetIO().DisplaySize.x * 0.5f, ImGui::GetIO().DisplaySize.y * 0.5f), ImGuiCond_FirstUseEver, ImVec2(0.5f, 0.5f));

        if (ImGui::Begin(g_keyboard_title, &g_show_virtual_keyboard, ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_AlwaysAutoResize)) {
            // 显示当前输入的文本
            ImGui::TextWrapped("当前输入: %s", g_keyboard_target_buffer);
            ImGui::Separator();

            // 计算按钮尺寸，确保有足够的边距
            float available_width = ImGui::GetContentRegionAvail().x;
            float button_width = (available_width - ImGui::GetStyle().ItemSpacing.x * 2) / 3.0f;
            float button_height = 50.0f; // 固定高度，更稳定

            // 数字键盘布局 (1-9)
            for (int i = 1; i <= 9; ++i) {
                if ((i - 1) % 3 != 0) ImGui::SameLine();
                if (ImGui::Button(std::to_string(i).c_str(), ImVec2(button_width, button_height))) {
                    size_t len = strlen(g_keyboard_target_buffer);
                    if (len < g_keyboard_target_buffer_size - 1) {
                        g_keyboard_target_buffer[len] = (char)('0' + i);
                        g_keyboard_target_buffer[len + 1] = '\0';
                    }
                }
            }

            // 底部一行：清除、0、退格
            if (ImGui::Button("清除", ImVec2(button_width, button_height))) {
                g_keyboard_target_buffer[0] = '\0';
            }
            ImGui::SameLine();
            if (ImGui::Button("0", ImVec2(button_width, button_height))) {
                size_t len = strlen(g_keyboard_target_buffer);
                if (len < g_keyboard_target_buffer_size - 1) { // 允许在空字符串时添加0
                    g_keyboard_target_buffer[len] = '0';
                    g_keyboard_target_buffer[len + 1] = '\0';
                }
            }
            ImGui::SameLine();
            if (ImGui::Button("退格", ImVec2(button_width, button_height))) {
                size_t len = strlen(g_keyboard_target_buffer);
                if (len > 0) {
                    g_keyboard_target_buffer[len - 1] = '\0';
                }
            }

            ImGui::Separator();

            // 添加确认和取消按钮
            float full_button_width = (available_width - ImGui::GetStyle().ItemSpacing.x) / 2.0f;
            if (ImGui::Button("确认", ImVec2(full_button_width, button_height))) {
                g_show_virtual_keyboard = false;
            }
            ImGui::SameLine();
            if (ImGui::Button("取消", ImVec2(full_button_width, button_height))) {
                g_show_virtual_keyboard = false;
            }
        }
        ImGui::End();
    }

    //================ 函数声明 ================//
    void Loop();
    void Render();
    void DrawUI();

    // GameUI销毁状态检查 - 使用SharedDataManager
    inline bool IsGameUIDestroying() {
        return SharedDataManager::IsGameUIDestroying();
    }

    // 重置GameUI销毁状态 - 使用SharedDataManager
    inline void ResetGameUIDestroyState() {
        SharedDataManager::ResetGameUIDestroyState();
    }

    // 检查PlayerController实例是否有效 - 使用SharedDataManager
    inline bool IsPlayerInstanceValid(void* player) {
        return SharedDataManager::IsPlayerInstanceValid(player);
    }

    // 清理函数已在上方前向声明

    //================ 简化的初始化函数 ================//
    void Init() {
        // 大部分初始化工作现在由SharedDataManager::Init()处理
        // 这里可以添加DrawHack特有的初始化逻辑
    }

    //================ 安全的主循环逻辑 ================//

    /**
     * @brief 主循环，现在使用SharedDataManager的GameUIController实例
     */
    void Loop() {
        // 首先检查GameUI是否正在销毁，如果是则立即停止所有绘制
        if (IsGameUIDestroying()) {
            PlayerRays::ClearRenderData();
            ItemESP::ClearRenderData();
            return;
        }

        // 检查SharedDataManager状态变化
        static auto last_check_time = std::chrono::steady_clock::now();
        auto current_time = std::chrono::steady_clock::now();
        auto elapsed_ms = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - last_check_time).count();

        // 每50ms检查一次状态
        if (elapsed_ms >= 50) {
            last_check_time = current_time;

            // 如果SharedDataManager状态不是Ready，或者实例为空，立即停止绘制
            if (SharedDataManager::g_status != SharedDataManager::Status::Ready ||
                SharedDataManager::g_GameUiController == nullptr) {
                PlayerRays::ClearRenderData();
                ItemESP::ClearRenderData();
                return;
            }

            // 额外检查：如果SharedDataManager长时间没有更新，也停止绘制
            auto shared_elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                current_time - SharedDataManager::g_last_update_timestamp).count();
            if (shared_elapsed > 200) { // 200ms没有更新就停止
                PlayerRays::ClearRenderData();
                ItemESP::ClearRenderData();
                return;
            }
        }

        // 基础安全检查
        if (SharedDataManager::g_status != SharedDataManager::Status::Ready ||
            SharedDataManager::g_GameUiController == nullptr) {
            PlayerRays::ClearRenderData();
            ItemESP::ClearRenderData();
            return;
        }

        // 当SharedDataManager重新获得GameUI实例时，重置销毁状态
        if (SharedDataManager::g_status == SharedDataManager::Status::Ready &&
            SharedDataManager::g_GameUiController != nullptr) {
            // 检查是否需要重置销毁状态
            if (IsGameUIDestroying()) {
                ResetGameUIDestroyState();
                // 调试计数器会在PlayerRays::ClearRenderData中被重置
            }
        }

        // 更新销毁对象快照（保留用于其他对象的销毁检测） - 使用SharedDataManager
        SharedDataManager::g_destroyed_snapshot = SharedDataManager::g_destroyed_objects_this_frame;
        SharedDataManager::g_destroyed_objects_this_frame.clear();

        // 执行各模块的更新逻辑
        try {
            PlayerRays::Loop();
            ItemESP::Loop();
        } catch (...) {
            // 捕获任何异常，防止闪退
            PlayerRays::ClearRenderData();
            ItemESP::ClearRenderData();
        }
    }

    //================ 玩家射线功能实现 ================//
    namespace PlayerRays {

        // 清空渲染数据的安全函数
        void ClearRenderData() {
            g_player_screen_positions.clear();
            g_player_controller_count = 0;
            g_last_collected_players = 0;
        }

        void Loop() {
            std::vector<ImVec2> current_frame_positions;

            // 首先检查GameUI是否正在销毁
            if (IsGameUIDestroying()) {
                ClearRenderData();
                return;
            }

            // 功能关闭检查
            if (!g_enable_esp) {
                ClearRenderData();
                return;
            }

            // 更早的安全检查：确保GameUI仍然有效且最近有更新
            if (SharedDataManager::g_status != SharedDataManager::Status::Ready ||
                SharedDataManager::g_GameUiController == nullptr) {
                ClearRenderData();
                return;
            }

            // 检查SharedDataManager的心跳，如果长时间没更新说明GameUI可能要销毁了
            auto current_time = std::chrono::steady_clock::now();
            auto elapsed_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                current_time - SharedDataManager::g_last_update_timestamp).count();
            if (elapsed_ms > 150) { // 150ms没有更新就停止绘制
                ClearRenderData();
                return;
            }

            // 检查必要的函数指针 - 使用SharedDataManager
            if (!SharedDataManager::Camera_get_main || !SharedDataManager::Object_FindObjectsOfType ||
                !SharedDataManager::g_playerControllerType ||
                !SharedDataManager::PlayerController_get_isSelfPlayer ||
                !SharedDataManager::PlayerController_get_position ||
                !SharedDataManager::Camera_WorldToScreenPoint) {
                g_enable_esp = false;
                ClearRenderData();
                return;
            }

            // 安全获取主摄像机
            void* mainCamera = nullptr;
            try {
                mainCamera = SharedDataManager::Camera_get_main();
            } catch (...) {
                // Camera访问失败，可能GameUI已销毁
                ClearRenderData();
                return;
            }

            if (!mainCamera) {
                ClearRenderData();
                return;
            }

            // 使用缓存的有效PlayerController实例
            std::vector<void*> allPlayers_copy;
            g_player_controller_count = SharedDataManager::g_valid_player_instances.size();

            if (SharedDataManager::g_valid_player_instances.empty()) {
                // 没有有效实例时，确保计数器为0
                g_player_controller_count = 0;
                g_last_collected_players = 0;
                ClearRenderData();
                return;
            }

            // 复制有效实例到本地向量
            allPlayers_copy.reserve(SharedDataManager::g_valid_player_instances.size());
            for (void* player : SharedDataManager::g_valid_player_instances) {
                allPlayers_copy.push_back(player);
            }

            // 找到本地玩家
            void* localPlayer = nullptr;
            for (void* p : allPlayers_copy) {
                try {
                    if (IsPlayerInstanceValid(p) && SharedDataManager::PlayerController_get_isSelfPlayer(p)) {
                        localPlayer = p;
                        break;
                    }
                } catch (...) { /* 忽略错误 */ }
            }

            float screen_height = ImGui::GetIO().DisplaySize.y;
            if (screen_height > 0) {
                for (void* player : allPlayers_copy) {
                    // 使用新的实例有效性检查
                    if (!IsPlayerInstanceValid(player)) {
                        continue; // 实例无效，跳过
                    }

                    // 主动检查：GameUI是否正在销毁
                    if (IsGameUIDestroying()) {
                        break; // GameUI正在销毁，立即停止处理
                    }

                    // 三重安全检查：确保GameUI仍然有效且最近有更新
                    if (SharedDataManager::g_GameUiController == nullptr) {
                        break; // GameUI已销毁，立即停止处理
                    }

                    // 检查心跳，如果PlayerListHack长时间没更新，立即停止
                    auto loop_current_time = std::chrono::steady_clock::now();
                    auto loop_elapsed_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                        loop_current_time - SharedDataManager::g_last_update_timestamp).count();
                    if (loop_elapsed_ms > 100) { // 在循环中更严格，100ms就停止
                        break;
                    }

                    try {
                        if (player == nullptr || (g_exclude_self && player == localPlayer)) continue;

                        Vector2 world_pos_2d = SharedDataManager::PlayerController_get_position(player);
                        Vector3 world_pos_3d = { world_pos_2d.X, world_pos_2d.Y, 0.0f };
                        Vector3 screen_pos_3d = SharedDataManager::Camera_WorldToScreenPoint(mainCamera, world_pos_3d);

                        if (screen_pos_3d.Z > 0) {
                            current_frame_positions.push_back(ImVec2(screen_pos_3d.X, screen_height - screen_pos_3d.Y - g_y_offset));
                        }
                    } catch (...) {
                        continue;
                    }
                }
            }

            // 记录本次收集到的玩家数量
            g_last_collected_players = current_frame_positions.size();

            // 更新全局渲染数据
            g_player_screen_positions = std::move(current_frame_positions);
        }

        void Render() {
            // 检查GameUI是否正在销毁，如果是则不渲染
            if (IsGameUIDestroying()) {
                return;
            }

            if (!g_enable_esp || g_player_screen_positions.empty()) {
                return;
            }

            ImDrawList* drawList = ImGui::GetBackgroundDrawList();
            float screen_width = ImGui::GetIO().DisplaySize.x;
            float screen_height = ImGui::GetIO().DisplaySize.y;

            // 根据设置确定射线起始点
            ImVec2 line_origin;
            switch (g_ray_origin) {
                case 0: // 顶部中心
                    line_origin = ImVec2(screen_width / 2.0f, 0.0f);
                    break;
                case 1: // 上方中间（距离顶部约1/6屏幕高度）
                    line_origin = ImVec2(screen_width / 2.0f, screen_height / 6.0f);
                    break;
                case 2: // 底部中心
                    line_origin = ImVec2(screen_width / 2.0f, screen_height);
                    break;
                case 3: // 屏幕中心
                    line_origin = ImVec2(screen_width / 2.0f, screen_height / 2.0f);
                    break;
                default:
                    line_origin = ImVec2(screen_width / 2.0f, 0.0f);
                    break;
            }

            // 转换颜色
            ImU32 color = ImGui::ColorConvertFloat4ToU32(g_ray_color);

            for (const auto& pos : g_player_screen_positions) {
                drawList->AddLine(line_origin, pos, color, g_line_thickness);
            }
        }

        void DrawUI() {
            // 显示PlayerListHack的状态
            switch (SharedDataManager::g_status) {
                case SharedDataManager::Status::NotReady:
                    ImGui::TextColored(ImVec4(1, 0, 0, 1), "状态: 未就绪");
                    break;
                case SharedDataManager::Status::Preparing:
                    ImGui::TextColored(ImVec4(1, 1, 0, 1), "状态: 准备中...");
                    break;
                case SharedDataManager::Status::Ready:
                    ImGui::TextColored(ImVec4(0, 1, 0, 1), "状态: 就绪");
                    break;
                case SharedDataManager::Status::FieldNotFound:
                    ImGui::TextColored(ImVec4(1, 0, 0, 1), "状态: Hook失败");
                    break;
            }

            // 显示PlayerController实例信息
            ImGui::Separator();
            ImGui::Text("PlayerController调试信息:");
            ImGui::Text("缓存实例数: %d", g_player_controller_count);
            ImGui::Text("收集到的玩家: %d", g_last_collected_players);
            ImGui::Text("GameUI销毁状态: %s", IsGameUIDestroying() ? "是" : "否");

            // 显示上次刷新时间
            auto current_time = std::chrono::steady_clock::now();
            auto elapsed_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                current_time - SharedDataManager::g_last_instance_refresh).count();
            ImGui::Text("上次刷新: %lld ms前", elapsed_ms);

            // 功能就绪后才显示相关控件
            if (SharedDataManager::g_status == SharedDataManager::Status::Ready) {
                ImGui::Separator();
                ImGui::Checkbox("绘制玩家射线", &g_enable_esp);
                ImGui::SameLine();
                ImGui::TextDisabled("(?)");
                if (ImGui::IsItemHovered()) {
                    ImGui::SetTooltip("向其他所有玩家绘制射线");
                }
                ImGui::Checkbox("排除自身", &g_exclude_self);
                ImGui::SliderFloat("线条粗细", &g_line_thickness, 1.0f, 10.0f, "%.1f");
                ImGui::SliderFloat("Y轴向上偏移", &g_y_offset, 0.0f, 300.0f, "%.0f px");

                // 新增：射线颜色设置
                ImGui::Separator();
                ImGui::Text("射线外观设置:");
                ImGui::ColorEdit4("射线颜色", (float*)&g_ray_color);

                // 新增：射线起始点设置
                const char* origin_items[] = { "顶部中心", "上方中间", "底部中心", "屏幕中心" };
                ImGui::Combo("射线起始点", &g_ray_origin, origin_items, IM_ARRAYSIZE(origin_items));

                // 手动重置销毁状态按钮
                ImGui::Separator();
                if (ImGui::Button("重置销毁状态")) {
                    ResetGameUIDestroyState();
                }
            }
        }
    }

    //================ 物品绘制功能实现 ================//
    namespace ItemESP {

        // 清空渲染数据的安全函数
        void ClearRenderData() {
            g_item_render_info.clear();
            g_item_counts.clear();
        }

        void Loop() {
            std::vector<ItemRenderInfo> current_frame_info;
            std::map<int, int> current_item_counts;

            // 首先检查GameUI是否正在销毁
            if (IsGameUIDestroying()) {
                ClearRenderData();
                return;
            }

            // 功能关闭检查
            if (!g_enable_esp && !g_show_counter) {
                ClearRenderData();
                return;
            }

            // 更早的安全检查：确保GameUI仍然有效且最近有更新
            if (SharedDataManager::g_status != SharedDataManager::Status::Ready ||
                SharedDataManager::g_GameUiController == nullptr) {
                ClearRenderData();
                return;
            }

            // 检查PlayerListHack的心跳，如果长时间没更新说明GameUI可能要销毁了
            auto current_time = std::chrono::steady_clock::now();
            auto elapsed_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                current_time - SharedDataManager::g_last_update_timestamp).count();
            if (elapsed_ms > 150) { // 150ms没有更新就停止绘制
                ClearRenderData();
                return;
            }

            // 检查必要的函数指针 - 使用SharedDataManager
            if (!SharedDataManager::Camera_get_main || !SharedDataManager::Object_FindObjectsOfType ||
                !SharedDataManager::g_objectControllerType ||
                !SharedDataManager::ObjectController_get_id ||
                !SharedDataManager::ObjectController_get_gridWdPos) {
                g_enable_esp = false;
                g_show_counter = false;
                ClearRenderData();
                return;
            }

            // 安全获取主摄像机
            void* mainCamera = nullptr;
            if (g_enable_esp) {
                try {
                    mainCamera = SharedDataManager::Camera_get_main();
                } catch (...) {
                    // Camera访问失败，可能GameUI已销毁
                    g_enable_esp = false;
                    ClearRenderData();
                    return;
                }

                if (!mainCamera) {
                    g_enable_esp = false;
                }
            }

            // 安全获取物品对象
            Array<void**>* allObjects_raw = nullptr;
            try {
                allObjects_raw = SharedDataManager::Object_FindObjectsOfType(SharedDataManager::g_objectControllerType);
            } catch (...) {
                // 对象查找失败，可能GameUI已销毁
                ClearRenderData();
                return;
            }

            if (allObjects_raw && allObjects_raw->getPointer() && allObjects_raw->getLength() > 0) {
                std::vector<void*> allObjects_copy;
                allObjects_copy.reserve(allObjects_raw->getLength());
                for (int i = 0; i < allObjects_raw->getLength(); ++i) {
                    void* obj_ptr = allObjects_raw->getPointer()[i];
                    if (obj_ptr) {
                        allObjects_copy.push_back(obj_ptr);
                    }
                }

                float screen_height = ImGui::GetIO().DisplaySize.y;
                for (void* obj : allObjects_copy) {
                    // 首先检查这个ObjectController是否已被销毁（快照）
                    if (SharedDataManager::g_destroyed_snapshot.count(obj)) {
                        continue;
                    }

                    // 实时检查：这个ObjectController是否在当前帧被销毁
                    if (SharedDataManager::g_destroyed_objects_this_frame.count(obj)) {
                        continue; // 这个ObjectController已被销毁，跳过
                    }

                    // 主动检查：GameUI是否正在销毁
                    if (IsGameUIDestroying()) {
                        break; // GameUI正在销毁，立即停止处理
                    }

                    // 三重安全检查：确保GameUI仍然有效且最近有更新
                    if (SharedDataManager::g_GameUiController == nullptr) {
                        break; // GameUI已销毁，立即停止处理
                    }

                    // 检查心跳，如果PlayerListHack长时间没更新，立即停止
                    auto loop_current_time = std::chrono::steady_clock::now();
                    auto loop_elapsed_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                        loop_current_time - SharedDataManager::g_last_update_timestamp).count();
                    if (loop_elapsed_ms > 100) { // 在循环中更严格，100ms就停止
                        break;
                    }

                    try {
                        if (!obj) continue;

                        // 应用过滤器
                        if (g_show_only_home_markers) {
                            if (!SharedDataManager::ObjectController_get_isHomeMarker || !SharedDataManager::ObjectController_get_isHomeMarker(obj)) {
                                continue;
                            }
                        }

                        int id = SharedDataManager::ObjectController_get_id(obj);

                        if (!g_id_filter_list.empty()) {
                            bool found_in_list = std::find(g_id_filter_list.begin(), g_id_filter_list.end(), id) != g_id_filter_list.end();
                            if (g_is_blacklist_mode ? found_in_list : !found_in_list) {
                                continue;
                            }
                        }

                        current_item_counts[id]++;

                        if (g_enable_esp && mainCamera && screen_height > 0) {
                            Vector2 world_pos_2d = SharedDataManager::ObjectController_get_gridWdPos(obj);
                            Vector3 world_pos_3d = { world_pos_2d.X, world_pos_2d.Y, 0.0f };
                            Vector3 screen_pos_3d = SharedDataManager::Camera_WorldToScreenPoint(mainCamera, world_pos_3d);

                            if (screen_pos_3d.Z > 0) {
                                // 获取物品颜色（自定义颜色或默认颜色）
                                ImVec4 item_color = g_default_color;
                                auto color_it = g_custom_colors.find(id);
                                if (color_it != g_custom_colors.end()) {
                                    item_color = color_it->second;
                                }

                                current_frame_info.push_back({
                                    ImVec2(screen_pos_3d.X, screen_height - screen_pos_3d.Y),
                                    id,
                                    item_color
                                });
                            }
                        }
                    } catch (...) {
                        continue;
                    }
                }
            }

            // 更新全局数据
            g_item_render_info = std::move(current_frame_info);
            g_item_counts = std::move(current_item_counts);
        }

        void Render() {
            // 检查GameUI是否正在销毁，如果是则不渲染
            if (IsGameUIDestroying()) {
                return;
            }

            if (!g_enable_esp || g_item_render_info.empty()) {
                return;
            }

            ImDrawList* drawList = ImGui::GetBackgroundDrawList();
            ImVec2 line_origin = ImVec2(ImGui::GetIO().DisplaySize.x / 2.0f, 0.0f);

            for (const auto& info : g_item_render_info) {
                // 使用物品的自定义颜色
                ImU32 color = ImGui::ColorConvertFloat4ToU32(info.color);

                if (g_enable_lines) {
                    drawList->AddLine(line_origin, info.screen_pos, color, g_line_thickness);
                }
                std::string text = std::to_string(info.id);
                drawList->AddText(nullptr, g_font_size, info.screen_pos, color, text.c_str());
            }
        }

        void RenderCounter() {
            // 检查GameUI是否正在销毁，如果是则不渲染
            if (IsGameUIDestroying()) {
                return;
            }

            if (!g_show_counter) return;

            if (g_item_counts.empty()) return;

            ImDrawList* drawList = ImGui::GetForegroundDrawList();
            ImVec2 pos = ImVec2(10.0f, 10.0f);
            float line_height = ImGui::GetTextLineHeightWithSpacing();
            ImU32 text_color = IM_COL32(255, 255, 255, 255);
            ImU32 bg_color = IM_COL32(0, 0, 0, 150);

            for (const auto& pair : g_item_counts) {
                std::string text = "ID: " + std::to_string(pair.first) + " | Count: " + std::to_string(pair.second);
                ImVec2 text_size = ImGui::CalcTextSize(text.c_str());
                drawList->AddRectFilled(pos, ImVec2(pos.x + text_size.x + 4, pos.y + text_size.y + 2), bg_color);
                drawList->AddText(ImVec2(pos.x + 2, pos.y + 1), text_color, text.c_str());
                pos.y += line_height;
            }
        }

        void DrawUI() {
            // 功能就绪后才显示相关控件
            if (SharedDataManager::g_status == SharedDataManager::Status::Ready) {
                ImGui::Checkbox("绘制物品ID", &g_enable_esp);
                ImGui::SameLine();
                ImGui::Checkbox("绘制射线", &g_enable_lines);
                ImGui::Checkbox("显示物品统计", &g_show_counter);
                ImGui::SliderFloat("字体大小", &g_font_size, 8.0f, 24.0f, "%.0f");
                ImGui::SliderFloat("射线粗细", &g_line_thickness, 1.0f, 10.0f, "%.1f");

                ImGui::Separator();
                ImGui::Text("颜色设置:");
                ImGui::ColorEdit4("默认颜色", (float*)&g_default_color);

                // 自定义颜色设置
                ImGui::Text("自定义物品颜色:");
                std::string color_input_text = "物品ID: " + std::string(g_id_input_buffer);
                if (ImGui::Button(color_input_text.c_str(), ImVec2(ImGui::GetContentRegionAvail().x * 0.6f, 0))) {
                    g_keyboard_target_buffer = g_id_input_buffer;
                    g_keyboard_target_buffer_size = sizeof(g_id_input_buffer);
                    g_keyboard_title = "输入物品ID";
                    g_show_virtual_keyboard = true;
                }
                ImGui::SameLine();
                if (ImGui::Button("设置颜色")) {
                    int id = std::atoi(g_id_input_buffer);
                    if (id > 0) {
                        g_color_setting_id = id;
                        // 如果已有自定义颜色，加载它
                        auto it = g_custom_colors.find(id);
                        if (it != g_custom_colors.end()) {
                            g_temp_color = it->second;
                        } else {
                            g_temp_color = g_default_color;
                        }
                    }
                }

                if (g_color_setting_id > 0) {
                    ImGui::Text("设置物品 %d 的颜色:", g_color_setting_id);
                    ImGui::ColorEdit4("物品颜色", (float*)&g_temp_color);
                    if (ImGui::Button("确认设置")) {
                        g_custom_colors[g_color_setting_id] = g_temp_color;
                        g_color_setting_id = 0;
                    }
                    ImGui::SameLine();
                    if (ImGui::Button("取消")) {
                        g_color_setting_id = 0;
                    }
                    ImGui::SameLine();
                    if (ImGui::Button("删除颜色")) {
                        g_custom_colors.erase(g_color_setting_id);
                        g_color_setting_id = 0;
                    }
                }

                ImGui::Separator();
                ImGui::Text("过滤器:");
                ImGui::Checkbox("只显示家园标记", &g_show_only_home_markers);

                ImGui::Separator();
                ImGui::Text("ID 过滤器:");
                ImGui::Checkbox("启用黑名单模式", &g_is_blacklist_mode);

                // 使用虚拟键盘的ID输入
                std::string btn_text = "ID: ";
                btn_text += (g_id_input_buffer[0] == '\0' ? "点击输入..." : g_id_input_buffer);
                if (ImGui::Button(btn_text.c_str(), ImVec2(ImGui::GetContentRegionAvail().x * 0.7f, 0))) {
                    g_keyboard_target_buffer = g_id_input_buffer;
                    g_keyboard_target_buffer_size = sizeof(g_id_input_buffer);
                    g_keyboard_title = "输入物品ID";
                    g_show_virtual_keyboard = true;
                }
                ImGui::SameLine();
                if (ImGui::Button("添加")) {
                    int id_to_add = std::atoi(g_id_input_buffer);
                    if (id_to_add > 0 && std::find(g_id_filter_list.begin(), g_id_filter_list.end(), id_to_add) == g_id_filter_list.end()) {
                        g_id_filter_list.push_back(id_to_add);
                    }
                    g_id_input_buffer[0] = '\0';
                }

                // 显示当前已添加的ID列表
                int id_to_remove = -1;
                for (int i = 0; i < g_id_filter_list.size(); ++i) {
                    ImGui::Text("  - %d", g_id_filter_list[i]);
                    ImGui::SameLine();
                    if (ImGui::SmallButton(("移除##" + std::to_string(i)).c_str())) {
                        id_to_remove = i;
                    }
                }
                if (id_to_remove != -1) {
                    g_id_filter_list.erase(g_id_filter_list.begin() + id_to_remove);
                }
            }
        }
    }

    //================ 主要接口函数 ================//

    /**
     * @brief 渲染所有绘制内容
     */
    void Render() {
        PlayerRays::Render();
        ItemESP::Render();
        ItemESP::RenderCounter();
    }

    /**
     * @brief 绘制ImGui界面
     */
    void DrawUI() {
        if (ImGui::CollapsingHeader("玩家射线")) {
            PlayerRays::DrawUI();
        }
        if (ImGui::CollapsingHeader("物品绘制")) {
            ItemESP::DrawUI();
        }

        // 显示虚拟键盘
        ShowVirtualKeyboard();
    }
}
