//
// Created by 33735 on 2025/06/24.
//

#pragma once

#include "imgui.h"
#include "GameHelper.h"
#include <cstdint>
#include <string>

namespace ObserverHack {

    // 状态定义
    enum class Status {
        NotReady,
        Preparing,
        FieldNotFound,
        Ready
    };

    // 全局变量
    Status g_status = Status::NotReady;
    bool g_observerModeEnabled = false;
    int32_t g_observerFieldOffset = -1;
    int32_t g_targetIdFieldOffset = -1; // 新增的目标ID字段偏移
    int g_observeTargetId = 0;

    // 准备功能的函数
    void Prepare() {
        g_status = Status::Preparing;

        // 查找两个字段的偏移
        g_observerFieldOffset = GameHelper::GetFieldOffset("Assembly-CSharp.dll", "", "GameServerManager", "<isObserverMode>k__BackingField");
        g_targetIdFieldOffset = GameHelper::GetFieldOffset("Assembly-CSharp.dll", "", "GameServerManager", "<observeTargetId>k__BackingField");

        if (g_observerFieldOffset != -1 && g_targetIdFieldOffset != -1) {
            g_status = Status::Ready;
        } else {
            g_status = Status::FieldNotFound;
        }
    }

    // 绘制UI
    void DrawUI() {
        // 状态显示
        switch (g_status) {
            case Status::NotReady:
            case Status::Preparing:
                ImGui::TextColored(ImVec4(1, 1, 0, 1), "状态: 正在准备功能...");
                break;
            case Status::FieldNotFound:
                ImGui::TextColored(ImVec4(1, 0, 0, 1), "状态: 错误, 字段未找到!");
                break;
            case Status::Ready:
                ImGui::TextColored(ImVec4(0, 1, 0, 1), "状态: 已就绪");
                break;
        }
        ImGui::Separator();

        // 功能开关
        ImGui::Checkbox("启用观察者模式", &g_observerModeEnabled);

        // 静态缓冲区，用于与键盘交互
        static char id_buf[16] = "0";

        // 目标ID输入框
        std::string btn_text = "观察目标ID: ";
        g_observeTargetId = atoi(id_buf); // 持续从缓冲区更新
        btn_text += (g_observeTargetId == 0 ? "Tap to enter..." : id_buf);

        if (ImGui::Button(btn_text.c_str())) {
            snprintf(id_buf, sizeof(id_buf), "%d", g_observeTargetId);

        }
    }

    void OnUpdate() {
    }

    // 主循环，由后台线程调用
    void Loop() {
        if (g_status == Status::Ready && (g_observerModeEnabled || g_observeTargetId != 0)) {
            try {
                void* gameServerManager = GameHelper::getGameServerManager();
                if (gameServerManager && g_observerFieldOffset != -1 && g_targetIdFieldOffset != -1) {
                    // 安全的内存写入，添加边界检查
                    try {
                        // 验证内存地址范围的合理性
                        uintptr_t base_addr = reinterpret_cast<uintptr_t>(gameServerManager);
                        uintptr_t observer_addr = base_addr + g_observerFieldOffset;
                        uintptr_t target_addr = base_addr + g_targetIdFieldOffset;

                        // 基本的地址合理性检查
                        if (base_addr > 0x1000 &&
                            observer_addr > base_addr && observer_addr < base_addr + 0x10000 &&
                            target_addr > base_addr && target_addr < base_addr + 0x10000) {

                            // 写入观察者模式布尔值
                            *reinterpret_cast<volatile bool*>(observer_addr) = g_observerModeEnabled;

                            // 写入观察目标ID整数值
                            *reinterpret_cast<volatile int*>(target_addr) = g_observeTargetId;
                        }
                    } catch (...) {
                        // 内存写入失败，可能对象已失效，重新准备
                        g_status = Status::NotReady;
                    }
                }
            } catch (...) {
                // GameServerManager获取失败，可能在场景切换中
                // 不改变状态，下次循环重试
            }
        }

        // 修复逻辑错误：只有在状态不是Ready且字段偏移未找到时才重新准备
        if (g_status == Status::NotReady) {
            Prepare();
        }
    }

    // 总初始化，在主初始化之后调用
    void Init() {
        Prepare();
    }
} 