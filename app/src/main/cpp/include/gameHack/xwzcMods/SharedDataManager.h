//
// Created by AI Assistant on 2025/08/25.
// 统一的数据管理模块，避免多个模块重复Hook和获取实例数据
//

#pragma once

#include "imgui.h"
#include "GameHelper.h"
#include "dobby.h"
#include <cstdint>
#include <vector>
#include <string>
#include <chrono>
#include <functional>
#include <mutex>
#include <unordered_set>
#include "Unity/Vector2.hpp"
#include "Unity/Vector3.hpp"

namespace SharedDataManager {

    //================ 状态枚举 ================//
    enum class Status {
        NotReady,       // 未准备好
        Preparing,      // 正在准备
        FieldNotFound,  // 未找到必要的字段或方法
        Ready           // 功能已就绪
    };

    //================ 全局状态 ================//
    Status g_status = Status::NotReady;
    std::chrono::steady_clock::time_point g_last_update_timestamp;
    bool g_gameui_is_destroying = false;

    //================ 共享实例数据 ================//
    void* g_GameUiController = nullptr;
    void* g_local_player_instance = nullptr;
    std::unordered_set<void*> g_valid_player_instances;
    std::chrono::steady_clock::time_point g_last_instance_refresh;

    //================ 共享类型缓存 ================//
    void* g_playerControllerType = nullptr;
    void* g_objectControllerType = nullptr;
    void* g_cameraType = nullptr;

    //================ 共享函数指针 ================//
    // 基础函数
    typedef void* (*GetTypeFunc_t)(String*);
    typedef Array<void**>* (*Object_FindObjectsOfType_t)(void*);
    GetTypeFunc_t Type_GetTypeName = nullptr;
    Object_FindObjectsOfType_t Object_FindObjectsOfType = nullptr;

    // PlayerController相关
    typedef bool (*PlayerController_get_isSelfPlayer_t)(void*);
    typedef Vector2 (*PlayerController_get_position_t)(void*);
    typedef void (*PlayerController_set_position_t)(void*, Vector2);
    PlayerController_get_isSelfPlayer_t PlayerController_get_isSelfPlayer = nullptr;
    PlayerController_get_position_t PlayerController_get_position = nullptr;
    PlayerController_set_position_t PlayerController_set_position_o = nullptr;

    // GameUIController相关
    typedef void (*PlayerListButtonClicked_t)(void*);
    typedef void (*GameUIController_Update_t)(void*);
    typedef void (*GameUIController_OnDestroy_t)(void*);
    PlayerListButtonClicked_t PlayerListButtonClicked = nullptr;
    GameUIController_Update_t o_GameUIController_Update = nullptr;
    GameUIController_OnDestroy_t o_GameUIController_OnDestroy = nullptr;

    // Camera相关
    typedef float (*get_orthographicSize_t)(void*);
    typedef void (*set_orthographicSize_t)(void*, float);
    typedef void* (*Camera_get_main_t)();
    typedef Vector3 (*Camera_WorldToScreenPoint_t)(void*, Vector3);
    get_orthographicSize_t get_orthographicSize = nullptr;
    set_orthographicSize_t set_orthographicSize = nullptr;
    Camera_get_main_t Camera_get_main = nullptr;
    Camera_WorldToScreenPoint_t Camera_WorldToScreenPoint = nullptr;

    // ObjectController相关
    typedef Vector2 (*ObjectController_get_gridWdPos_t)(void*);
    typedef int (*ObjectController_get_id_t)(void*);
    typedef bool (*ObjectController_get_isHomeMarker_t)(void*);
    ObjectController_get_gridWdPos_t ObjectController_get_gridWdPos = nullptr;
    ObjectController_get_id_t ObjectController_get_id = nullptr;
    ObjectController_get_isHomeMarker_t ObjectController_get_isHomeMarker = nullptr;

    // 销毁检测相关
    typedef void (*PlayerController_OnDestroy_t)(void*);
    PlayerController_OnDestroy_t o_PlayerController_OnDestroy = nullptr;
    std::unordered_set<void*> g_destroyed_objects_this_frame;
    std::unordered_set<void*> g_destroyed_snapshot;

    //================ 任务队列系统 ================//
    std::vector<std::function<void()>> g_tasks;
    std::mutex g_tasks_mutex;

    void AddTask(const std::function<void()>& task) {
        std::lock_guard<std::mutex> lock(g_tasks_mutex);
        g_tasks.push_back(task);
    }

    void ProcessTasks() {
        std::vector<std::function<void()>> tasks_to_run;
        {
            std::lock_guard<std::mutex> lock(g_tasks_mutex);
            if (!g_tasks.empty()) {
                tasks_to_run.swap(g_tasks);
            }
        }

        for (const auto& task : tasks_to_run) {
            task();
        }
    }

    //================ Hook函数实现 ================//
    
    // Hook GameUIController::Update
    void GameUIController_Update_Hook(void* __this) {
        g_GameUiController = __this;
        g_status = Status::Ready;
        g_last_update_timestamp = std::chrono::steady_clock::now();

        ProcessTasks(); // 处理任务队列

        // 调用原始函数
        if (o_GameUIController_Update) {
            o_GameUIController_Update(__this);
        }
    }

    // Hook GameUIController::OnDestroy
    void GameUIController_OnDestroy_Hook(void* __this) {
        g_gameui_is_destroying = true;
        g_valid_player_instances.clear();

        // 调用原始函数
        if (o_GameUIController_OnDestroy) {
            o_GameUIController_OnDestroy(__this);
        }
    }

    // Hook PlayerController::OnDestroy
    void PlayerController_OnDestroy_Hook(void* __this) {
        g_destroyed_objects_this_frame.insert(__this);
        g_valid_player_instances.erase(__this);

        // 调用原始函数
        if (o_PlayerController_OnDestroy) {
            o_PlayerController_OnDestroy(__this);
        }
    }

    //================ 实例管理函数 ================//
    
    // 刷新PlayerController实例
    void RefreshPlayerInstances() {
        if (g_status != Status::Ready || g_GameUiController == nullptr || g_gameui_is_destroying) {
            return;
        }

        auto current_time = std::chrono::steady_clock::now();
        auto elapsed_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            current_time - g_last_instance_refresh).count();
        if (elapsed_ms < 500) {
            return;
        }

        g_last_instance_refresh = current_time;

        try {
            Array<void**>* allPlayers_raw = Object_FindObjectsOfType(g_playerControllerType);
            if (allPlayers_raw && allPlayers_raw->getPointer() && allPlayers_raw->getLength() > 0) {
                g_valid_player_instances.clear();
                g_local_player_instance = nullptr;

                for (int i = 0; i < allPlayers_raw->getLength(); ++i) {
                    void* player_ptr = allPlayers_raw->getPointer()[i];
                    if (player_ptr) {
                        g_valid_player_instances.insert(player_ptr);
                        
                        // 查找本地玩家
                        try {
                            if (PlayerController_get_isSelfPlayer && PlayerController_get_isSelfPlayer(player_ptr)) {
                                g_local_player_instance = player_ptr;
                            }
                        } catch (...) {
                            // 忽略单个玩家检查失败
                        }
                    }
                }
            }
        } catch (...) {
            g_valid_player_instances.clear();
            g_local_player_instance = nullptr;
        }
    }

    // 检查PlayerController实例是否有效
    bool IsPlayerInstanceValid(void* player) {
        if (!player) return false;
        if (g_destroyed_snapshot.count(player)) return false;
        if (g_destroyed_objects_this_frame.count(player)) return false;
        return g_valid_player_instances.count(player) > 0;
    }

    // 检查GameUI是否正在销毁
    bool IsGameUIDestroying() {
        return g_gameui_is_destroying;
    }

    // 重置GameUI销毁状态
    void ResetGameUIDestroyState() {
        g_gameui_is_destroying = false;
    }

    //================ 主循环逻辑 ================//
    void Loop() {
        // 检查状态变化
        if (g_status == Status::Ready) {
            auto elapsed_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now() - g_last_update_timestamp).count();
            if (elapsed_ms > 500) {
                g_GameUiController = nullptr;
                g_status = Status::Preparing;
            }
        }

        // 更新销毁对象快照
        g_destroyed_snapshot = g_destroyed_objects_this_frame;
        g_destroyed_objects_this_frame.clear();

        // 刷新实例
        if (g_status == Status::Ready && g_GameUiController != nullptr) {
            if (g_gameui_is_destroying) {
                ResetGameUIDestroyState();
                g_destroyed_objects_this_frame.clear();
                g_destroyed_snapshot.clear();
            }
            RefreshPlayerInstances();
        }
    }

    //================ 初始化函数 ================//
    void Init() {
        g_status = Status::NotReady;

        // 获取基础函数指针
        Type_GetTypeName = (GetTypeFunc_t)Il2CppGetMethodOffset("mscorlib.dll", "System", "Type", "GetType", 1);
        Object_FindObjectsOfType = (Object_FindObjectsOfType_t)Il2CppGetMethodOffset("UnityEngine.dll", "UnityEngine", "Object", "FindObjectsOfType", 1);

        // 获取PlayerController相关函数
        PlayerController_get_isSelfPlayer = (PlayerController_get_isSelfPlayer_t)Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "PlayerController", "get_isSelfPlayer", 0);
        PlayerController_get_position = (PlayerController_get_position_t)Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "PlayerController", "get_position", 0);
        void* set_position_method = Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "PlayerController", "set_position", 1);

        // 获取GameUIController相关函数
        PlayerListButtonClicked = (PlayerListButtonClicked_t)Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "GameUIController", "PlayerListButtonClicked", 0);
        void* update_method = Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "GameUIController", "Update", 0);
        void* gameUIOnDestroyAddr = Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "GameUIController", "OnDestroy", 0);

        // 获取Camera相关函数
        get_orthographicSize = (get_orthographicSize_t)Il2CppGetMethodOffset("UnityEngine.CoreModule.dll", "UnityEngine", "Camera", "get_orthographicSize", 0);
        set_orthographicSize = (set_orthographicSize_t)Il2CppGetMethodOffset("UnityEngine.CoreModule.dll", "UnityEngine", "Camera", "set_orthographicSize", 1);
        Camera_get_main = (Camera_get_main_t)Il2CppGetMethodOffset("UnityEngine.CoreModule.dll", "UnityEngine", "Camera", "get_main", 0);
        Camera_WorldToScreenPoint = (Camera_WorldToScreenPoint_t)Il2CppGetMethodOffset("UnityEngine.CoreModule.dll", "UnityEngine", "Camera", "WorldToScreenPoint", 1);

        // 获取ObjectController相关函数
        ObjectController_get_gridWdPos = (ObjectController_get_gridWdPos_t)Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "ObjectController", "get_gridWdPos", 0);
        ObjectController_get_id = (ObjectController_get_id_t)Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "ObjectController", "get_id", 0);
        ObjectController_get_isHomeMarker = (ObjectController_get_isHomeMarker_t)Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "ObjectController", "get_isHomeMarker", 0);

        // 获取销毁检测相关函数
        void* playerOnDestroyAddr = Il2CppGetMethodOffset("Assembly-CSharp.dll", "", "PlayerController", "OnDestroy", 0);

        // 预缓存类型
        if (Type_GetTypeName) {
            g_playerControllerType = Type_GetTypeName(Il2CppString::Create("PlayerController, Assembly-CSharp"));
            g_objectControllerType = Type_GetTypeName(Il2CppString::Create("ObjectController, Assembly-CSharp"));
            g_cameraType = Type_GetTypeName(Il2CppString::Create("UnityEngine.Camera, UnityEngine.CoreModule"));
        }

        // 执行Hook操作
        if (update_method) {
            DobbyHook(update_method, (void*)GameUIController_Update_Hook, (void**)&o_GameUIController_Update);
        }
        if (gameUIOnDestroyAddr) {
            DobbyHook(gameUIOnDestroyAddr, (void*)GameUIController_OnDestroy_Hook, (void**)&o_GameUIController_OnDestroy);
        }
        if (playerOnDestroyAddr) {
            DobbyHook(playerOnDestroyAddr, (void*)PlayerController_OnDestroy_Hook, (void**)&o_PlayerController_OnDestroy);
        }
        if (set_position_method) {
            // 保存原始函数指针，但不在这里Hook
            // Hook将由PlayerSelfHack模块负责，使用我们提供的函数指针
            PlayerController_set_position_o = (PlayerController_set_position_t)set_position_method;
        }

        if (!PlayerListButtonClicked || !update_method) {
            g_status = Status::FieldNotFound;
            return;
        }

        g_status = Status::Preparing;
        g_last_update_timestamp = std::chrono::steady_clock::now();
    }
}
