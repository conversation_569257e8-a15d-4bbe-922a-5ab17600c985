//
// Created by 33735 on 2025/06/24.
//

#pragma once

#include "imgui.h"
#include "GameHelper.h"
#include "SharedDataManager.h"
#include <cstdint>
#include <vector>
#include <string>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <functional>
#include <mutex>
#include "Unity/Vector2.hpp"
#include "Unity/Vector3.hpp"

namespace PlayerSelfHack {

    // 视野相关全局变量
    Array<void**>* g_allCameras = nullptr;
    void* g_cameraType = nullptr;
    float g_ortho_size = 5.0f; // 默认视口大小
    bool g_initial_size_set = false;

    // 位置修改相关全局变量
    bool g_position_hack_enabled = false;
    int g_position_offset_x = 0; // X偏移量（整数）
    int g_position_offset_y = 0; // Y偏移量（整数）
    Vector2 g_current_position = {0.0f, 0.0f}; // 当前位置（用于显示）
    Vector2 g_original_position = {0.0f, 0.0f}; // 开关打开时获取的原始坐标
    bool g_original_position_captured = false; // 是否已获取原始坐标

    // 注意：现在使用SharedDataManager中的共享数据和函数指针
    // g_local_player_instance -> SharedDataManager::g_local_player_instance
    // PlayerController相关函数 -> SharedDataManager中的对应函数

    // 修改摄像机视野
    void ChangeCameraFov(float size) {
        // 只修改第二个摄像机（索引为1）
        if (g_allCameras && g_allCameras->getLength() > 1) {
            void* secondCamera = g_allCameras->getPointer()[1];
            if (secondCamera) {
                SharedDataManager::set_orthographicSize(secondCamera, size);
            }
        }
    }

    // Hook PlayerController::set_position 函数 - 现在通过SharedDataManager管理
    void PlayerController_set_position_Hook(void* this_ptr, Vector2 value) {
        // 安全检查：确保this_ptr不为空
        if (!this_ptr) {
            return;
        }

        try {
            // 如果是本地玩家且位置修改开关打开
            if (this_ptr == SharedDataManager::g_local_player_instance &&
                SharedDataManager::g_local_player_instance != nullptr &&
                g_position_hack_enabled &&
                SharedDataManager::IsPlayerInstanceValid(this_ptr)) { // 确保玩家实例仍然有效
                // 如果还没有获取原始坐标，获取当前传入的坐标作为原始坐标
                if (!g_original_position_captured) {
                    g_original_position = value;
                    g_original_position_captured = true;
                }

                // 每次都新建一个Vector2实例：原始坐标 + 偏移量
                Vector2 modified_position;
                modified_position.X = g_original_position.X + (float)g_position_offset_x;
                modified_position.Y = g_original_position.Y + (float)g_position_offset_y;

                // 拦截：传入新建的Vector2实例
                if (SharedDataManager::PlayerController_set_position_o) {
                    SharedDataManager::PlayerController_set_position_o(this_ptr, modified_position);
                }
            } else {
                // 不是本地玩家或开关关闭，直接调用原始函数
                if (SharedDataManager::PlayerController_set_position_o) {
                    SharedDataManager::PlayerController_set_position_o(this_ptr, value);
                }
            }
        } catch (...) {
            // 发生异常时调用原始函数，避免崩溃
            if (SharedDataManager::PlayerController_set_position_o) {
                SharedDataManager::PlayerController_set_position_o(this_ptr, value);
            }
        }
    }

    // 重置位置修改状态（销毁状态）
    void ResetPositionHack() {
        g_original_position_captured = false;
        g_original_position = {0.0f, 0.0f};
    }

    // 绘制UI
    void DrawUI() {
        // --- Camera FOV UI ---
        ImGui::Separator();
        if (ImGui::SliderFloat("视野大小", &g_ortho_size, 1.0f, 20.0f)) {
            ChangeCameraFov(g_ortho_size);
        }

        // --- Position Hack UI ---
        ImGui::Separator();
        ImGui::Text("--- 位置修改 ---");

        // 安全显示当前位置
        try {
            ImGui::Text("当前位置: (%.2f, %.2f)", g_current_position.X, g_current_position.Y);
            if (g_position_hack_enabled && g_original_position_captured) {
                ImGui::Text("原始坐标: (%.2f, %.2f)", g_original_position.X, g_original_position.Y);
                // 实时计算并显示修改后的坐标
                float modified_x = g_original_position.X + (float)g_position_offset_x;
                float modified_y = g_original_position.Y + (float)g_position_offset_y;
                ImGui::Text("修改坐标: (%.2f, %.2f)", modified_x, modified_y);
            }
        } catch (...) {
            ImGui::Text("位置信息: 获取失败");
        }
        ImGui::Separator();

        // 安全状态检查 - 使用SharedDataManager
        bool functions_available = (SharedDataManager::PlayerController_get_isSelfPlayer != nullptr &&
                                   SharedDataManager::PlayerController_set_position_o != nullptr &&
                                   SharedDataManager::Object_FindObjectsOfType != nullptr);

        if (!functions_available) {
            ImGui::TextColored(ImVec4(1, 0, 0, 1), "警告: 必要函数未初始化，功能不可用");
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1), "请等待游戏完全加载后重新初始化");
            return; // 提前返回，避免显示不可用的控件
        }

        // 监控开关状态变化
        bool previous_enabled = g_position_hack_enabled;
        ImGui::Checkbox("启用位置偏移", &g_position_hack_enabled);

        // 如果开关被关闭，销毁状态
        if (previous_enabled && !g_position_hack_enabled) {
            ResetPositionHack();
        }

        ImGui::Text("位置偏移量 (整数单位):");
        ImGui::SliderInt("X偏移", &g_position_offset_x, -100, 100);
        ImGui::SliderInt("Y偏移", &g_position_offset_y, -100, 100);

        if (ImGui::Button("重置偏移")) {
            try {
                g_position_offset_x = 0;
                g_position_offset_y = 0;
            } catch (...) {
                // 重置失败时的处理
            }
        }

        ImGui::SameLine();
        if (ImGui::Button("重新获取原始坐标")) {
            if (g_position_hack_enabled) {
                ResetPositionHack();
            }
        }

        ImGui::Text("本地玩家实例: %p", SharedDataManager::g_local_player_instance);

        // 显示当前偏移状态和安全信息 - 使用SharedDataManager
        if (g_position_hack_enabled && SharedDataManager::g_local_player_instance && g_original_position_captured) {
            ImGui::TextColored(ImVec4(0, 1, 0, 1), "位置偏移已激活");
            ImGui::TextColored(ImVec4(0, 1, 0, 1), "偏移量: (%d, %d)",
                              g_position_offset_x, g_position_offset_y);
        } else if (g_position_hack_enabled && SharedDataManager::g_local_player_instance && !g_original_position_captured) {
            ImGui::TextColored(ImVec4(1, 1, 0, 1), "等待获取原始坐标...");
        } else if (g_position_hack_enabled && !SharedDataManager::g_local_player_instance) {
            ImGui::TextColored(ImVec4(1, 1, 0, 1), "等待找到本地玩家...");
        } else {
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1), "位置偏移已禁用");
        }

        // 添加安全提示
        ImGui::Separator();
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1), "提示: 获取原始坐标+偏移量，每次新建Vector2实例传入");
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1), "关闭开关时销毁状态，如遇闪退请重启游戏后重新初始化");
    }

    // 主循环，由后台线程调用
    void Loop() {
        // 安全检查：确保必要的函数指针可用 - 使用SharedDataManager
        if (!SharedDataManager::g_cameraType || !SharedDataManager::Object_FindObjectsOfType) return;

        // --- 检测本地玩家实例是否被销毁 ---
        if (SharedDataManager::g_local_player_instance) {
            // 检查本地玩家是否在销毁列表中
            if (!SharedDataManager::IsPlayerInstanceValid(SharedDataManager::g_local_player_instance)) {
                // 本地玩家已被销毁，清空位置修改相关状态
                ResetPositionHack();
                g_current_position = {0.0f, 0.0f};
            }
        } else {
            // 没有本地玩家实例，也清空状态
            if (g_original_position_captured) {
                ResetPositionHack();
                g_current_position = {0.0f, 0.0f};
            }
        }

        // --- 摄像机相关逻辑 ---
        try {
            g_allCameras = SharedDataManager::Object_FindObjectsOfType(SharedDataManager::g_cameraType);
        } catch (...) {
            // FindObjectsOfType失败，可能在场景切换中
            g_allCameras = nullptr;
        }

        if (!g_initial_size_set && g_allCameras && g_allCameras->getLength() > 1 && SharedDataManager::get_orthographicSize) {
            try {
                void* secondCamera = g_allCameras->getPointer()[1];
                if (secondCamera) {
                    g_ortho_size = SharedDataManager::get_orthographicSize(secondCamera);
                    g_initial_size_set = true;
                }
            } catch (...) {
                // 相机操作失败，下次重试
            }
        }

        // --- 更新当前位置显示 ---
        // 现在使用SharedDataManager中的本地玩家实例
        if (SharedDataManager::g_local_player_instance && SharedDataManager::PlayerController_get_position) {
            try {
                g_current_position = SharedDataManager::PlayerController_get_position(SharedDataManager::g_local_player_instance);
            } catch (...) {
                // 获取位置失败，可能玩家已被销毁，清空当前位置
                g_current_position = {0.0f, 0.0f};
            }
        } else {
            // 没有本地玩家实例或函数指针，清空当前位置
            g_current_position = {0.0f, 0.0f};
        }
    }

    // 总初始化，在主初始化之后调用
    void Init() {
        // 大部分初始化工作现在由SharedDataManager::Init()处理

        // Hook set_position方法 - 使用SharedDataManager提供的函数指针
        if (SharedDataManager::PlayerController_set_position_o) {
            try {
                void* set_position_method = (void*)SharedDataManager::PlayerController_set_position_o;
                int hook_result = DobbyHook(set_position_method, (void*)PlayerController_set_position_Hook, (void**)&SharedDataManager::PlayerController_set_position_o);
                if (hook_result != 0) {
                    // Hook失败
                }
            } catch (...) {
                // Hook过程中发生异常
            }
        }
    }

    void OnUpdate() {
        // 主线程任务处理入口
        // 视野功能通过Loop()实现，这里不需要额外处理
    }
}
