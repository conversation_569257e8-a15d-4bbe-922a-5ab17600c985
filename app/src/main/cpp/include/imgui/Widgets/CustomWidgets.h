// CustomWidgets.h
#pragma once

#include "ImGui/imgui.h"
#include <map>
#include <string>


namespace CustomWidgets {
    inline float ImLerp(float a, float b, float t) {
        return a + (b - a) * t;
    }

    inline float ImClamp(float v, float mn, float mx) {
        return (v < mn) ? mn : (v > mx) ? mx : v;
    }

    inline int ImFormatString(char *buf, size_t buf_size, const char *fmt, ...) {
        va_list args;
        va_start(args, fmt);
        int ret = vsnprintf(buf, buf_size, fmt, args);
        va_end(args);
        return ret;
    }

    bool LineageSwitch(const char *label, bool *v) {
        ImGuiStyle &style = ImGui::GetStyle();
        ImVec2 p = ImGui::GetCursorScreenPos();
        ImDrawList *draw_list = ImGui::GetWindowDrawList();

        float height = ImGui::GetFrameHeight();
        float width = height * 1.8f;
        float radius = height * 0.5f;

        ImGui::InvisibleButton(label, ImVec2(width, height));
        bool is_clicked = ImGui::IsItemClicked();
        bool is_hovered = ImGui::IsItemHovered();

        // 动画过渡
        static std::map<std::string, float> animations;
        std::string key = label;
        float &t = animations[key];
        if (is_clicked) *v = !*v;
        t = *v ? CustomWidgets::ImLerp(t, 1.0f, ImGui::GetIO().DeltaTime * 10.0f)
               : CustomWidgets::ImLerp(t, 0.0f,
                                       ImGui::GetIO().DeltaTime *
                                       10.0f);

        // 绘制背景
        ImU32 col_bg = ImGui::GetColorU32(
                *v ? ImVec4(0.0f, 0.6f, 0.4f, 1.0f) : ImVec4(0.2f, 0.2f, 0.2f, 1.0f));
        draw_list->AddRectFilled(p, ImVec2(p.x + width, p.y + height), col_bg, height * 0.5f);

        // 绘制滑块
        float knob_x = p.x + radius + (width - 2 * radius) * t;
        ImU32 col_knob = ImGui::GetColorU32(ImVec4(1.0f, 1.0f, 1.0f, 1.0f));
        draw_list->AddCircleFilled(ImVec2(knob_x, p.y + radius), radius * 0.8f, col_knob);

        // 绘制标签
        ImGui::SameLine();
        ImGui::Text("%s", label);
        return is_clicked;
    }

    bool LineageSliderFloat(const char *label, float *v, float v_min, float v_max,
                            const char *format = "%.2f") {
        ImGuiStyle &style = ImGui::GetStyle();
        ImDrawList *draw_list = ImGui::GetWindowDrawList();
        ImVec2 p = ImGui::GetCursorScreenPos();

        float height = ImGui::GetFrameHeight() * 0.5f;
        float width = ImGui::GetContentRegionAvail().x - style.ItemSpacing.x;
        float radius = height * 0.5f;

        // 滑块交互区域
        ImGui::InvisibleButton(label, ImVec2(width, height));
        bool is_active = ImGui::IsItemActive();

        // 计算目标滑块位置
        float t_target = (*v - v_min) / (v_max - v_min);
        static float t_current = 0.0f; // 存储当前动画位置
        const float smooth_factor = 0.2f; // 动画平滑因子，值越小越平滑
        t_current += (t_target - t_current) * smooth_factor; // 插值实现平滑动画
        float knob_x = p.x + t_current * (width - 2 * radius);

        // 绘制背景
        ImU32 col_bg = ImGui::GetColorU32(ImVec4(0.2f, 0.2f, 0.2f, 1.0f));
        draw_list->AddRectFilled(p, ImVec2(p.x + width, p.y + height), col_bg, height * 0.5f);

        // 绘制进度条
        ImU32 col_bar = ImGui::GetColorU32(ImVec4(0.0f, 0.6f, 0.4f, 1.0f));
        draw_list->AddRectFilled(p, ImVec2(knob_x + radius, p.y + height), col_bar, height * 0.5f);

        // 绘制滑块
        ImU32 col_knob = ImGui::GetColorU32(ImVec4(1.0f, 1.0f, 1.0f, 1.0f));
        draw_list->AddCircleFilled(ImVec2(knob_x + radius, p.y + height / 2), radius * 0.8f,
                                   col_knob);

        // 处理拖动
        bool value_changed = false;
        if (is_active && ImGui::IsMouseDragging(0)) {
            float mouse_x = ImGui::GetIO().MousePos.x;
            t_target = (mouse_x - p.x) / (width - 2 * radius);
            t_target = CustomWidgets::ImClamp(t_target, 0.0f, 1.0f);
            *v = v_min + t_target * (v_max - v_min);
            value_changed = true;
        }

        // 绘制标签（左侧）
        ImGui::SetCursorScreenPos(ImVec2(p.x, p.y + height + 4.0f));
        ImGui::Text("%s", label);

        // 绘制值（右侧）
        char buffer[32];
        CustomWidgets::ImFormatString(buffer, sizeof(buffer), format, *v);
        ImVec2 text_size = ImGui::CalcTextSize(buffer);
        ImGui::SetCursorScreenPos(ImVec2(p.x + width - text_size.x, p.y + height + 4.0f));
        ImGui::Text("%s", buffer);

        // 移动光标到下一行
        ImGui::SetCursorScreenPos(
                ImVec2(p.x, p.y + height + ImGui::GetTextLineHeightWithSpacing() + 4.0f));

        return value_changed;
    }
}
