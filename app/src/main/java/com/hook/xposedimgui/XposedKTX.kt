package com.hook.xposedimgui

import de.robv.android.xposed.XC_MethodHook
import de.robv.android.xposed.XC_MethodReplacement
import de.robv.android.xposed.XposedBridge
import de.robv.android.xposed.XposedHelpers
import java.util.ArrayList

fun Any.invoke(methodName: String, vararg params: Any?): Any? {
    return XposedImpl.callMethod(this, methodName, params)
}

fun String.invoke(classLoader: ClassLoader, methodName: String, vararg params: Any?): Any? {
    return XposedImpl.callStaticMethod(
        XposedHelpers.findClass(this, classLoader),
        methodName,
        params
    )
}

fun String.hook(
    classLoader: ClassLoader,
    methodName: String,
    vararg paramsType: Any?,
    before: (param: XC_MethodHook.MethodHookParam) -> Unit = {},
    after: (param: XC_MethodHook.MethodHookParam) -> Unit = {}
) {
    val list = ArrayList<Any>()
    for (pt in paramsType)
        pt?.let { list.add(it) }
    list.add(object : XC_MethodHook() {
        override fun beforeHookedMethod(param: MethodHookParam?) {
            param?.let { before.invoke(it) }
        }

        override fun afterHookedMethod(param: MethodHookParam?) {
            param?.let { after.invoke(it) }
        }
    })
    XposedImpl.hookMethod(this, classLoader, methodName, list.toArray())
}

fun String.replaceMethod(
    classLoader: ClassLoader,
    methodName: String,
    vararg paramsType: Any?,
    call: (param: XC_MethodHook.MethodHookParam) -> Any = { }
) {
    val list = ArrayList<Any>()
    for (pt in paramsType)
        pt?.let { list.add(it) }
    list.add(object : XC_MethodReplacement() {
        override fun replaceHookedMethod(p0: MethodHookParam?): Any {
            return call.invoke(p0!!)
        }
    })
    XposedImpl.hookMethod(this, classLoader, methodName, list.toArray())
}

fun Class<*>.hook(
    methodName: String,
    vararg paramsType: Any?,
    before: (param: XC_MethodHook.MethodHookParam) -> Unit = {},
    after: (param: XC_MethodHook.MethodHookParam) -> Unit = {}
) {
    val list = ArrayList<Any>()
    for (pt in paramsType)
        pt?.let { list.add(it) }
    list.add(object : XC_MethodHook() {
        override fun beforeHookedMethod(param: MethodHookParam?) {
            param?.let { before.invoke(it) }
        }

        override fun afterHookedMethod(param: MethodHookParam?) {
            param?.let { after.invoke(it) }
        }
    })
    XposedImpl.hookMethod(this, methodName, list.toArray())
}

fun Class<*>.replace(
    methodName: String,
    vararg paramsType: Any?,
    call: (param: XC_MethodHook.MethodHookParam) -> Any = { }
) {
    val list = ArrayList<Any>()
    for (pt in paramsType)
        pt?.let { list.add(it) }
    list.add(object : XC_MethodReplacement() {
        override fun replaceHookedMethod(p0: MethodHookParam?): Any {
            return call.invoke(p0!!)
        }
    })
    XposedImpl.hookMethod(this, methodName, list.toArray())
}

fun <T> XC_MethodHook.MethodHookParam.obj(): T {
    return this.thisObject as T
}

fun XC_MethodHook.MethodHookParam.invoke(vararg args: Any?): Any? {
    return XposedBridge.invokeOriginalMethod(this.method, this, args)
}
