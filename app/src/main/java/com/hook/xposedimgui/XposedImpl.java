package com.hook.xposedimgui;

import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedHelpers;

public class XposedImpl {
    public static Object callMethod(Object obj, String methodName, Object[] args) {
        return XposedHelpers.callMethod(obj, methodName, args);
    }

    public static Object callStaticMethod(Class<?> clazz, String methodName, Object[] args) {
        return XposedHelpers.callStaticMethod(clazz, methodName, args);
    }

    public static XC_MethodHook.Unhook hookMethod(String clazzName,
                                                  ClassLoader loader,
                                                  String methodName,
                                                  Object[] paramTypes) {
        return XposedHelpers.findAndHookMethod(
                clazzName, loader, methodName, paramTypes
        );
    }

    public static XC_MethodHook.Unhook hookMethod(Class<?> clazz,
                                                  String methodName,
                                                  Object[] paramTypes) {
        return XposedHelpers.findAndHookMethod(
                clazz, methodName, paramTypes
        );
    }
}
