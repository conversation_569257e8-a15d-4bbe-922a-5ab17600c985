package com.hook.xposedimgui

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import de.robv.android.xposed.IXposedHookLoadPackage
import de.robv.android.xposed.IXposedHookZygoteInit
import de.robv.android.xposed.callbacks.XC_LoadPackage
import java.io.File
import android.view.MotionEvent


class Core : IXposedHookLoadPackage, IXposedHookZygoteInit {

    var modulePath = ""

    override fun initZygote(p0: IXposedHookZygoteInit.StartupParam?) {
        modulePath = p0?.modulePath ?: ""
    }


    @SuppressLint("UnsafeDynamicallyLoadedCode")
    override fun handleLoadPackage(p0: XC_LoadPackage.LoadPackageParam) {
        Application::class.java.hook("attach", Context::class.java, after = { args ->
            args.obj<Application>().packageManager.let {
                try {
                    val moduleFile = File(modulePath).parent
                    System.load(File(moduleFile, "lib/arm64/libcore.so").absolutePath)
                } catch (_: Throwable) {
                }
            }
        })

        // Hook Activity 的 dispatchTouchEvent 来获取触摸事件
        Activity::class.java.hook("dispatchTouchEvent", MotionEvent::class.java, before = { param ->
            val motionEvent = param.args[0] as MotionEvent
            handleTouchEvent(motionEvent)
            // 若 ImGui 需要捕获鼠标，则消费事件，避免触摸穿透到游戏
            if (NativeBridge.wantCapture()) {
                param.result = true
            }
        })
    }

    private fun handleTouchEvent(ev: MotionEvent) {
        try {
            val action = ev.actionMasked
            val index = ev.actionIndex
            val x = ev.getX(index)
            val y = ev.getY(index)
            val down = action == MotionEvent.ACTION_DOWN || action == MotionEvent.ACTION_POINTER_DOWN
            val up = action == MotionEvent.ACTION_UP || action == MotionEvent.ACTION_POINTER_UP || action == MotionEvent.ACTION_CANCEL
            // 仅传递一个主指针即可驱动 ImGui（后续可扩展多指）
            NativeBridge.onTouch(action, x, y, down, up)
        } catch (_: Throwable) {
        }
    }
}

// Native 触摸桥接（同文件内定义，避免新建文件）
object NativeBridge {
    @JvmStatic external fun onTouch(action: Int, x: Float, y: Float, down: Boolean, up: Boolean)
    @JvmStatic external fun wantCapture(): Boolean
}