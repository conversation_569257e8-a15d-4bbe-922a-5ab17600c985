# 游戏修改核心技术文档：基于 `xdkp_menu.h` 的实战解析

本文档将以 `xdkp_menu.h` 文件中的代码为蓝本，详细阐述在基于 IL2CPP 的 Unity 游戏中，如何利用 IL2CPP 交互能力与 DobbyHook 框架协同工作，实现对游戏行为的修改。

### 目录
1.  **核心概念**
    *   IL2CPP：我们的“游戏地图”
    *   DobbyHook：我们的“改装工具”
    *   `xdkp_menu.h` 的工作流程：“侦查”与“行动”
2.  **第一阶段：侦查 (IL2CPP 交互)**
    *   2.1 获取单例/静态实例：`getPlayerControl` 函数解析
    *   2.2 获取函数地址：以 `get_PlayCoin` 为例
    *   2.3 获取成员变量偏移量：以 `gravity` 为例
3.  **第二阶段：行动 (修改游戏行为)**
    *   3.1 方法一：直接内存读写 (修改数据)
    *   3.2 方法二：函数 Hook (修改逻辑)
4.  **`xdkp_menu.h` 完整实战案例剖析**
    *   案例一：实现“无重力” (内存读写)
    *   案例二：实现“局内大量金币” (函数 Hook)
5.  **附录：关键函数签名**

---

### 1. 核心概念

#### **IL2CPP：我们的“游戏地图”**
IL2CPP 是 Unity 的一项技术，它将 C# 代码编译为本地库 (`libil2cpp.so`)。最重要的是，它保留了一份**元数据（Metadata）**，这份元数据就是 C# 原始代码结构（如类、方法名）与编译后内存地址之间的“映射表”或“地图”。我们项目中的 `Il2Cpp.h/.cpp` 就是这个“地图”的查询工具。

#### **DobbyHook：我们的“改装工具”**
DobbyHook 是一个强大的 `Inline Hook` 框架。它通过直接修改内存中目标函数的机器码，插入一个“跳转”指令，将程序的执行流强行重定向到我们预先写好的新函数中。它是修改游戏核心逻辑的“终极改装工具”。

#### **`xdkp_menu.h` 的工作流程：“侦查”与“行动”**
`xdkp_menu.h` 的所有功能都遵循这一流程：
1.  **侦查**：使用 `Il2Cpp...` 系列函数，将目标（如 `"PlayerControl.gravity"`）从一个字符串名称，定位到它在内存中的具体地址或偏移量。
2.  **行动**：根据“侦查”到的信息，选择合适的方式进行修改：
    *   **直接内存读写**：修改一个变量的值（如重力、无敌状态）。
    *   **DobbyHook**：替换一个函数的逻辑（如获取金币数量）。

---

### 2. 第一阶段：侦查 (IL2CPP 交互)

#### 2.1 获取单例/静态实例：`getPlayerControl` 函数解析
在 `xdkp_menu.h` 中，几乎所有的操作都需要一个 `PlayerControl` 的实例。`getPlayerControl` 函数展示了如何通过静态字段获取这个核心实例。

*   **作用**：获取 `PlayerControl` 类的全局唯一实例。
*   **代码示例** (`xdkp_menu.h`):
    ```cpp
    // 获取玩家控制对象的指针
    long getPlayerControl() {
        long PlayerControl = 0; // 初始化为0
        // 使用 Il2CppGetStaticFieldValue 获取名为 "self" 的静态字段的值
        Il2CppGetStaticFieldValue("BearGame.dll", "", "PlayerControl", "self", &PlayerControl);
        return PlayerControl; // 返回获取到的实例指针
    }
    ```
    *   **原理**：`Il2CppGetStaticFieldValue` 在“地图”中查找 `PlayerControl` 类的静态变量 `self`，并将它的值（即 `PlayerControl` 的实例地址）存入我们提供的 `&PlayerControl` 中。

#### 2.2 获取函数地址：以 `get_PlayCoin` 为例
这是使用 `DobbyHook` 的前置步骤，用于获取你想要 Hook 的那个函数的入口地址。

*   **作用**：根据类名和方法名，查询其在内存中的起始地址。
*   **代码示例** (`xdkp_menu.h` 的 `InitXWZCDobbyHook` 函数):
    ```cpp
    DobbyHook(
        // 使用 Il2CppGetMethodOffset 获取 get_PlayCoin 方法的地址
        (void *) Il2CppGetMethodOffset("BearGame.dll", "", "PlayerControl", "get_PlayCoin", 0),
        (void *) get_PlayCoin,
        (void **) &old_get_PlayCoin
    );
    ```
    *   **原理**：`Il2CppGetMethodOffset` 在“地图”中查找 `PlayerControl.get_PlayCoin()`，并返回它第一条机器指令的内存地址。

#### 2.3 获取成员变量偏移量：以 `gravity` 为例
当你想修改一个**对象**的**成员变量**时（例如玩家的重力），你需要知道这个变量在该对象内存布局中的“相对位置”。

*   **作用**：获取一个成员变量相对于其对象实例起始地址的字节偏移量。
*   **代码示例** (`xdkp_menu.h` 的 `ShowXWZCMenu` 函数):
    ```cpp
    WriteFloat(
        getPlayerControl() + // 1. 获取 PlayerControl 实例的起始地址
        Il2CppGetFieldOffset("BearGame.dll", "", "PlayerControl", "gravity"), // 2. 获取 gravity 字段的偏移量
        gravityValue // 3. 在“起始地址 + 偏移量”的位置写入新值
    );
    ```
    *   **原理**：`Il2CppGetFieldOffset` 告诉我们，“gravity”这个变量永远存储在一个 `PlayerControl` 对象内存块的起始地址往后 `X` 个字节的地方。

---

### 3. 第二阶段：行动 (修改游戏行为)

#### 3.1 方法一：直接内存读写 (修改数据)

当你知道了一个对象的实例地址和其成员的偏移量后，就可以直接修改它的值。

*   **场景**：修改玩家无敌状态、游泳状态、重力值等。
*   **代码示例** (`xdkp_menu.h` 的“无敌”功能):
    ```cpp
    // 1. "侦查"：获取 PlayerControl 实例，并获取 "invincible" 字段的偏移量
    // 2. "行动"：在计算出的精确地址上，写入 1 (开启) 或 0 (关闭)
    WriteByte(getPlayerControl() +
              Il2CppGetFieldOffset("BearGame.dll", "", "PlayerControl", "invincible"),
              isInvincibleEnabled ? 1 : 0);

    // 对无敌时间做同样的操作
    WriteFloat(getPlayerControl() +
               Il2CppGetFieldOffset("BearGame.dll", "", "PlayerControl", "invincibleTime"),
               isInvincibleEnabled ? std::numeric_limits<float>::max() : 0.0f);
    ```
    *   `WriteByte` 和 `WriteFloat` 是为了方便而封装的函数，其内部就是简单的指针写入操作。

#### 3.2 方法二：函数 Hook (修改逻辑)

这是最强大和最灵活的方式，它从根本上改变了游戏的行为逻辑。

*   **场景**：实现“局内大量金币”、“大量分数”、“大量距离”等功能。
*   **代码示例** (`xdkp_menu.h` 的“局内大量金币”功能):

    ```cpp
    // --- 准备阶段 ---

    // 1. 定义函数指针，用于保存原始函数功能
    int (*old_get_PlayCoin)(void *instance) = nullptr;

    // 2. 创建我们的新函数 (Hook 函数)
    int get_PlayCoin(void *instance) {
        if (getPlayCoinEnabled && instance != nullptr) {
            // 如果功能开启，直接返回一个假数据
            return 114514;
        }
        // 否则，调用原始函数，返回真实数据
        return old_get_PlayCoin(instance);
    }

    // --- 应用阶段 (在 InitXWZCDobbyHook 中) ---
    DobbyHook(
        (void *) Il2CppGetMethodOffset("BearGame.dll", "", "PlayerControl", "get_PlayCoin", 0),
        (void *) get_PlayCoin,
        (void **) &old_get_PlayCoin
    );
    ```
    *   **原理**：我们没有去修改存储金币的变量，而是拦截了“游戏查询金币数量”这个**行为**本身，并直接给它一个我们想要的答案。这比反复修改变量要稳定和高效得多。

---

### 4. `xdkp_menu.h` 完整实战案例剖析

#### 案例一：实现“无重力” (内存读写)
```cpp
// 在 ImGui::Checkbox 的 if 判断中
if (ImGui::Checkbox("无重力", &isNoGravityEnabled)) {
    // 1. 根据复选框状态，决定要写入的重力值
    float gravityValue = isNoGravityEnabled ? 0.0f : 300.0f;

    // 2. "侦查" + "行动"
    WriteFloat(
        getPlayerControl() + // 获取实例基地址
        Il2CppGetFieldOffset("BearGame.dll", "", "PlayerControl", "gravity"), // 获取'gravity'偏移量
        gravityValue // 在最终地址写入新值
    );
}
```
这是一个典型的“内存读写”案例。逻辑简单直接：找到变量地址 -> 写入新值。

#### 案例二：实现“局内大量金币” (函数 Hook)
```cpp
// Hook 函数
int get_PlayCoin(void *instance) {
    if (getPlayCoinEnabled && instance != nullptr) {
        return 114514;
    }
    return old_get_PlayCoin(instance);
}

// 在 ImGui 中只有一个简单的开关
ImGui::Checkbox("局内大量金币", &getPlayCoinEnabled);

// 在初始化时设置 Hook
// DobbyHook(...);
```
这是一个典型的“函数 Hook”案例。UI 界面只负责切换一个布尔开关，所有复杂的逻辑都在被 Hook 的函数中完成。当游戏调用 `get_PlayCoin` 时，我们的代码会先介入，检查开关状态，再决定是返回假数据还是调用原始函数。

---

### 5. 附录：关键函数签名

```cpp
// 获取方法地址
void* Il2CppGetMethodOffset(const char* assemblyName, const char* namespaze, const char* className, const char* methodName, int argsCount);

// 获取字段偏移量
int Il2CppGetFieldOffset(const char* assemblyName, const char* namespaze, const char* className, const char* fieldName);

// 获取静态字段值
void Il2CppGetStaticFieldValue(const char* assemblyName, const char* namespaze, const char* className, const char* fieldName, void* output);

// DobbyHook
#include "dobby.h"
int DobbyHook(void* function_address, void* replacement_address, void** original_function_address_ptr);
``` 